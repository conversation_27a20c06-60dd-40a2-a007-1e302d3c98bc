import Router from './utils/router.js';
import WelcomePresenter from './presenters/WelcomePresenter.js';
import TestPresenter from './presenters/TestPresenter.js';
import AppModel from './models/AppModel.js';
import { baseStyles, applyStyles } from './styles/base.js';

// Initialize the application
class App {
  constructor() {
    this.router = new Router();
    this.model = new AppModel();
    this.currentPresenter = null;

    this.setupRoutes();
    this.setupGlobalStyles();
  }

  setupGlobalStyles() {
    // Apply base styles to body
    applyStyles(document.body, baseStyles.body);

    // Apply reset styles to all elements
    const style = document.createElement('style');
    style.textContent = `
      *, *::before, *::after {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      #app {
        width: 100%;
        height: 100vh;
      }
    `;
    document.head.appendChild(style);
  }

  setupRoutes() {
    this.router.addRoute('/', () => this.showWelcome());
    this.router.addRoute('/test', () => this.showTest());
  }

  showWelcome() {
    this.destroyCurrentPresenter();
    this.currentPresenter = new WelcomePresenter();
    this.currentPresenter.init();
    this.model.setCurrentPage('welcome');
  }

  showTest() {
    this.destroyCurrentPresenter();
    this.currentPresenter = new TestPresenter();
    this.currentPresenter.init();
    this.model.setCurrentPage('test');
  }

  destroyCurrentPresenter() {
    if (this.currentPresenter) {
      this.currentPresenter.destroy();
      this.currentPresenter = null;
    }
  }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new App();
});
