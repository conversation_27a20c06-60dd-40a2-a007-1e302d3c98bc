class AppModel {
  constructor() {
    this.currentPage = 'welcome';
    this.isRecording = false;
    this.testText = 'I went to the store to buy some groceries. The store was busy, and there was a long line at the checkout. I still managed to get everything I needed before going home.';

    // Recording state
    this.recordingState = {
      isRecording: false,
      isPaused: false,
      duration: 0,
      startTime: null,
      currentRecordingId: null
    };

    // Current recording data
    this.currentRecording = {
      audioData: null,
      audioBlob: null,
      duration: 0,
      sampleRate: 16000,
      channels: 1,
      size: 0
    };

    // Recording metadata
    this.recordingMetadata = {
      name: '',
      category: 'Speaking Test',
      date: null,
      notes: ''
    };

    // Recording history
    this.recordings = [];
    this.selectedRecordingId = null;
  }

  setCurrentPage(page) {
    this.currentPage = page;
  }

  getCurrentPage() {
    return this.currentPage;
  }

  setRecording(status) {
    this.isRecording = status;
    this.recordingState.isRecording = status;

    if (status) {
      this.recordingState.startTime = Date.now();
      this.recordingState.currentRecordingId = this.generateRecordingId();
    } else {
      this.recordingState.startTime = null;
    }
  }

  isCurrentlyRecording() {
    return this.isRecording;
  }

  getTestText() {
    return this.testText;
  }

  // Recording state management
  getRecordingState() {
    return { ...this.recordingState };
  }

  updateRecordingDuration(duration) {
    this.recordingState.duration = duration;
  }

  pauseRecording() {
    this.recordingState.isPaused = true;
  }

  resumeRecording() {
    this.recordingState.isPaused = false;
  }

  stopRecording() {
    this.recordingState.isRecording = false;
    this.recordingState.isPaused = false;
    this.isRecording = false;
  }

  // Current recording data management
  setCurrentRecordingData(data) {
    this.currentRecording = {
      ...this.currentRecording,
      ...data
    };
  }

  getCurrentRecordingData() {
    return { ...this.currentRecording };
  }

  clearCurrentRecording() {
    this.currentRecording = {
      audioData: null,
      audioBlob: null,
      duration: 0,
      sampleRate: 16000,
      channels: 1,
      size: 0
    };
  }

  // Recording metadata management
  setRecordingMetadata(metadata) {
    this.recordingMetadata = {
      ...this.recordingMetadata,
      ...metadata
    };
  }

  getRecordingMetadata() {
    return { ...this.recordingMetadata };
  }

  generateRecordingName() {
    const now = new Date();
    const dateStr = now.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
    return `Speaking Test - ${dateStr}`;
  }

  // Recording history management
  addRecording(recording) {
    this.recordings.unshift(recording); // Add to beginning (newest first)
  }

  setRecordings(recordings) {
    this.recordings = recordings;
  }

  getRecordings() {
    return [...this.recordings];
  }

  getRecordingById(id) {
    return this.recordings.find(r => r.id === id);
  }

  removeRecording(id) {
    this.recordings = this.recordings.filter(r => r.id !== id);
  }

  setSelectedRecording(id) {
    this.selectedRecordingId = id;
  }

  getSelectedRecording() {
    return this.selectedRecordingId ? this.getRecordingById(this.selectedRecordingId) : null;
  }

  // Utility methods
  generateRecordingId() {
    return `rec-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  formatDuration(seconds) {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }
}

export default AppModel;
