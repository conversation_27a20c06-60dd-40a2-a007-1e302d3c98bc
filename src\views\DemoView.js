import { baseStyles, applyStyles } from '../styles/base.js';
import TransitionController from '../utils/TransitionController.js';

class DemoView {
  constructor() {
    this.container = null;
  }

  render() {
    // Create main container
    this.container = document.createElement('div');
    applyStyles(this.container, {
      ...baseStyles.container,
      padding: '2rem',
      gap: '2rem'
    });

    // Create title
    const title = document.createElement('h1');
    title.textContent = 'Transition Demo';
    applyStyles(title, {
      fontSize: '2rem',
      fontWeight: '600',
      color: '#2d3748',
      marginBottom: '2rem'
    });

    // Create transition test buttons
    const buttonContainer = document.createElement('div');
    applyStyles(buttonContainer, {
      display: 'flex',
      flexDirection: 'column',
      gap: '1rem',
      alignItems: 'center'
    });

    // Button to test Welcome to Test transition
    const welcomeToTestBtn = this.createButton(
      'Test Welcome → Test Transition',
      () => {
        TransitionController.navigateWelcomeToTest(() => {
          window.location.hash = '#/test';
        });
      }
    );

    // Button to test Test to Welcome transition
    const testToWelcomeBtn = this.createButton(
      'Test Test → Welcome Transition',
      () => {
        TransitionController.navigateTestToWelcome(() => {
          window.location.hash = '#/';
        });
      }
    );

    // Button to go to Welcome (direct)
    const goToWelcomeBtn = this.createButton(
      'Go to Welcome (Direct)',
      () => {
        window.location.hash = '#/';
      }
    );

    // Button to go to Test (direct)
    const goToTestBtn = this.createButton(
      'Go to Test (Direct)',
      () => {
        window.location.hash = '#/test';
      }
    );

    // Current transition info
    const infoDiv = document.createElement('div');
    applyStyles(infoDiv, {
      marginTop: '2rem',
      padding: '1rem',
      backgroundColor: '#f7fafc',
      borderRadius: '0.5rem',
      border: '1px solid #e2e8f0'
    });

    const infoText = document.createElement('p');
    infoText.innerHTML = `
      <strong>Transition Types:</strong><br>
      • <strong>Welcome → Test:</strong> Microphone morphs from center to floating<br>
      • <strong>Test → Welcome:</strong> Microphone morphs from floating to center<br>
      • <strong>Direct Navigation:</strong> Uses router's automatic transition detection<br>
      • <strong>Initial Load:</strong> No transition on first page load
    `;
    applyStyles(infoText, {
      fontSize: '0.9rem',
      lineHeight: '1.6',
      color: '#4a5568'
    });

    infoDiv.appendChild(infoText);

    // Append elements
    buttonContainer.appendChild(welcomeToTestBtn);
    buttonContainer.appendChild(testToWelcomeBtn);
    buttonContainer.appendChild(goToWelcomeBtn);
    buttonContainer.appendChild(goToTestBtn);

    this.container.appendChild(title);
    this.container.appendChild(buttonContainer);
    this.container.appendChild(infoDiv);

    return this.container;
  }

  createButton(text, onClick) {
    const button = document.createElement('button');
    button.textContent = text;
    applyStyles(button, {
      padding: '0.75rem 1.5rem',
      backgroundColor: '#4299e1',
      color: 'white',
      border: 'none',
      borderRadius: '0.5rem',
      cursor: 'pointer',
      fontSize: '1rem',
      fontWeight: '500',
      transition: 'all 0.3s ease',
      minWidth: '250px'
    });

    button.addEventListener('mouseenter', () => {
      applyStyles(button, {
        backgroundColor: '#3182ce',
        transform: 'translateY(-2px)',
        boxShadow: '0 4px 12px rgba(66, 153, 225, 0.3)'
      });
    });

    button.addEventListener('mouseleave', () => {
      applyStyles(button, {
        backgroundColor: '#4299e1',
        transform: 'translateY(0)',
        boxShadow: 'none'
      });
    });

    button.addEventListener('click', onClick);

    return button;
  }

  destroy() {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
  }
}

export default DemoView;
