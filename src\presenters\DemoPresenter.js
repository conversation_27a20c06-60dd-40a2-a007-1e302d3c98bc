import DemoView from '../views/DemoView.js';

class DemoPresenter {
  constructor() {
    this.view = null;
  }

  init() {
    this.view = new DemoView();
    this.render();
  }

  render() {
    // Clear existing content
    const appElement = document.getElementById('app');
    appElement.innerHTML = '';
    
    // Render the view
    const viewElement = this.view.render();
    appElement.appendChild(viewElement);
  }

  destroy() {
    if (this.view) {
      this.view.destroy();
      this.view = null;
    }
  }
}

export default DemoPresenter;
