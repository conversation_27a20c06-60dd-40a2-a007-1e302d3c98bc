/**
 * WAVEncoder - Converts Float32Array audio data to WAV format
 * Supports mono and stereo audio with configurable sample rates
 */
class WAVEncoder {
  /**
   * Convert Float32Array audio data to WAV file blob
   * @param {Float32Array} audioData - Raw audio data (-1.0 to 1.0)
   * @param {number} sampleRate - Sample rate in Hz (e.g., 16000, 44100)
   * @param {number} channels - Number of channels (1 for mono, 2 for stereo)
   * @returns {Blob} WAV file as blob
   */
  static encodeWAV(audioData, sampleRate = 16000, channels = 1) {
    const length = audioData.length;
    const buffer = new ArrayBuffer(44 + length * 2); // WAV header (44 bytes) + 16-bit PCM data
    const view = new DataView(buffer);
    
    // WAV file header
    this.writeString(view, 0, 'RIFF');                    // ChunkID
    view.setUint32(4, 36 + length * 2, true);            // ChunkSize
    this.writeString(view, 8, 'WAVE');                    // Format
    
    // Format sub-chunk
    this.writeString(view, 12, 'fmt ');                   // Subchunk1ID
    view.setUint32(16, 16, true);                         // Subchunk1Size (PCM = 16)
    view.setUint16(20, 1, true);                          // AudioFormat (PCM = 1)
    view.setUint16(22, channels, true);                   // NumChannels
    view.setUint32(24, sampleRate, true);                 // SampleRate
    view.setUint32(28, sampleRate * channels * 2, true);  // ByteRate
    view.setUint16(32, channels * 2, true);               // BlockAlign
    view.setUint16(34, 16, true);                         // BitsPerSample
    
    // Data sub-chunk
    this.writeString(view, 36, 'data');                   // Subchunk2ID
    view.setUint32(40, length * 2, true);                 // Subchunk2Size
    
    // Convert float samples to 16-bit PCM
    this.floatTo16BitPCM(view, 44, audioData);
    
    return new Blob([buffer], { type: 'audio/wav' });
  }

  /**
   * Write string to DataView at specified offset
   */
  static writeString(view, offset, string) {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  }

  /**
   * Convert Float32Array (-1.0 to 1.0) to 16-bit PCM
   */
  static floatTo16BitPCM(output, offset, input) {
    for (let i = 0; i < input.length; i++, offset += 2) {
      // Clamp to [-1.0, 1.0] and convert to 16-bit signed integer
      const sample = Math.max(-1, Math.min(1, input[i]));
      output.setInt16(offset, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
    }
  }

  /**
   * Create a WAV file with metadata for download
   * @param {Float32Array} audioData - Raw audio data
   * @param {number} sampleRate - Sample rate in Hz
   * @param {number} channels - Number of channels
   * @param {string} filename - Filename for download
   * @returns {Object} File info with blob and metadata
   */
  static createWAVFile(audioData, sampleRate = 16000, channels = 1, filename = 'recording.wav') {
    const wavBlob = this.encodeWAV(audioData, sampleRate, channels);
    const duration = audioData.length / sampleRate;
    const size = wavBlob.size;
    
    return {
      blob: wavBlob,
      filename,
      duration,
      size,
      sampleRate,
      channels,
      mimeType: 'audio/wav'
    };
  }

  /**
   * Download WAV file to user's device
   * @param {Float32Array} audioData - Raw audio data
   * @param {number} sampleRate - Sample rate in Hz
   * @param {number} channels - Number of channels
   * @param {string} filename - Filename for download
   */
  static downloadWAV(audioData, sampleRate = 16000, channels = 1, filename = 'recording.wav') {
    const fileInfo = this.createWAVFile(audioData, sampleRate, channels, filename);
    
    // Create download link
    const url = URL.createObjectURL(fileInfo.blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    
    // Trigger download
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up object URL
    URL.revokeObjectURL(url);
    
    console.log(`📁 Downloaded: ${filename} (${(fileInfo.size / 1024).toFixed(1)} KB, ${fileInfo.duration.toFixed(1)}s)`);
    
    return fileInfo;
  }

  /**
   * Convert WAV blob to base64 string for storage
   * @param {Blob} wavBlob - WAV file blob
   * @returns {Promise<string>} Base64 encoded string
   */
  static async blobToBase64(wavBlob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64 = reader.result.split(',')[1]; // Remove data:audio/wav;base64, prefix
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(wavBlob);
    });
  }

  /**
   * Convert base64 string back to WAV blob
   * @param {string} base64String - Base64 encoded WAV data
   * @returns {Blob} WAV file blob
   */
  static base64ToBlob(base64String) {
    const binaryString = atob(base64String);
    const bytes = new Uint8Array(binaryString.length);
    
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    
    return new Blob([bytes], { type: 'audio/wav' });
  }

  /**
   * Get audio file information from WAV blob
   * @param {Blob} wavBlob - WAV file blob
   * @returns {Promise<Object>} Audio file metadata
   */
  static async getAudioInfo(wavBlob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        try {
          const buffer = reader.result;
          const view = new DataView(buffer);
          
          // Read WAV header information
          const channels = view.getUint16(22, true);
          const sampleRate = view.getUint32(24, true);
          const dataSize = view.getUint32(40, true);
          const duration = dataSize / (sampleRate * channels * 2); // 2 bytes per sample
          
          resolve({
            channels,
            sampleRate,
            duration,
            size: wavBlob.size,
            dataSize,
            mimeType: 'audio/wav'
          });
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = reject;
      reader.readAsArrayBuffer(wavBlob);
    });
  }

  /**
   * Validate WAV file format
   * @param {Blob} wavBlob - WAV file blob to validate
   * @returns {Promise<boolean>} True if valid WAV file
   */
  static async isValidWAV(wavBlob) {
    try {
      const buffer = await wavBlob.slice(0, 12).arrayBuffer();
      const view = new DataView(buffer);
      
      // Check RIFF header
      const riff = String.fromCharCode(
        view.getUint8(0), view.getUint8(1), view.getUint8(2), view.getUint8(3)
      );
      
      // Check WAVE format
      const wave = String.fromCharCode(
        view.getUint8(8), view.getUint8(9), view.getUint8(10), view.getUint8(11)
      );
      
      return riff === 'RIFF' && wave === 'WAVE';
    } catch (error) {
      console.error('❌ WAV validation error:', error);
      return false;
    }
  }
}

export default WAVEncoder;
