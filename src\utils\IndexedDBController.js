/**
 * IndexedDBController - Manages audio recording storage in IndexedDB
 * Stores recordings with metadata: name, date, category, duration, size, file
 */
class IndexedDBController {
  constructor() {
    this.dbName = 'AureaVoiceDB';
    this.dbVersion = 1;
    this.storeName = 'recordings';
    this.db = null;
  }

  /**
   * Initialize IndexedDB connection
   */
  async initialize() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => {
        console.error('❌ Failed to open IndexedDB:', request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        console.log('💾 IndexedDB initialized successfully');
        resolve(this.db);
      };

      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // Create recordings object store if it doesn't exist
        if (!db.objectStoreNames.contains(this.storeName)) {
          const store = db.createObjectStore(this.storeName, { 
            keyPath: 'id',
            autoIncrement: false 
          });

          // Create indexes for efficient querying
          store.createIndex('name', 'name', { unique: false });
          store.createIndex('date', 'date', { unique: false });
          store.createIndex('category', 'category', { unique: false });
          store.createIndex('duration', 'duration', { unique: false });
          store.createIndex('size', 'size', { unique: false });

          console.log('🏗️ Created recordings object store with indexes');
        }
      };
    });
  }

  /**
   * Generate unique recording ID with "rec-" prefix and UUID
   */
  generateRecordingId() {
    // Generate UUID v4
    const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
    
    return `rec-${uuid}`;
  }

  /**
   * Save audio recording to IndexedDB
   * @param {Object} recordingData - Recording data and metadata
   * @returns {Promise<string>} Recording ID
   */
  async saveRecording(recordingData) {
    if (!this.db) {
      await this.initialize();
    }

    const {
      audioBlob,
      name = 'Untitled Recording',
      category = 'General',
      duration = 0,
      sampleRate = 16000,
      channels = 1
    } = recordingData;

    try {
      // Generate unique ID
      const id = this.generateRecordingId();
      
      // Convert blob to base64 for storage
      const base64Data = await this.blobToBase64(audioBlob);
      
      // Create recording record
      const record = {
        id,
        name,
        date: new Date().toISOString(),
        category,
        duration: Math.round(duration * 100) / 100, // Round to 2 decimal places
        size: audioBlob.size,
        sampleRate,
        channels,
        mimeType: 'audio/wav',
        audioData: base64Data,
        createdAt: Date.now(),
        updatedAt: Date.now()
      };

      // Save to IndexedDB
      await this.addRecord(record);
      
      console.log(`💾 Saved recording: ${id} (${name})`);
      console.log(`📊 Size: ${(record.size / 1024).toFixed(1)} KB, Duration: ${duration.toFixed(1)}s`);
      
      return id;
    } catch (error) {
      console.error('❌ Failed to save recording:', error);
      throw error;
    }
  }

  /**
   * Add record to IndexedDB
   */
  async addRecord(record) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const request = store.add(record);

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Get recording by ID
   * @param {string} id - Recording ID
   * @returns {Promise<Object|null>} Recording data or null if not found
   */
  async getRecording(id) {
    if (!this.db) {
      await this.initialize();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      const request = store.get(id);

      request.onsuccess = () => {
        const result = request.result;
        if (result) {
          // Convert base64 back to blob
          result.audioBlob = this.base64ToBlob(result.audioData);
          delete result.audioData; // Remove base64 data to save memory
        }
        resolve(result);
      };
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Get all recordings with optional filtering
   * @param {Object} filters - Optional filters (category, dateRange, etc.)
   * @returns {Promise<Array>} Array of recording metadata (without audio data)
   */
  async getAllRecordings(filters = {}) {
    if (!this.db) {
      await this.initialize();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      const request = store.getAll();

      request.onsuccess = () => {
        let recordings = request.result;
        
        // Remove audio data for listing (keep only metadata)
        recordings = recordings.map(record => {
          const { audioData, ...metadata } = record;
          return metadata;
        });

        // Apply filters
        if (filters.category) {
          recordings = recordings.filter(r => r.category === filters.category);
        }
        
        if (filters.dateFrom) {
          recordings = recordings.filter(r => new Date(r.date) >= new Date(filters.dateFrom));
        }
        
        if (filters.dateTo) {
          recordings = recordings.filter(r => new Date(r.date) <= new Date(filters.dateTo));
        }

        // Sort by date (newest first)
        recordings.sort((a, b) => new Date(b.date) - new Date(a.date));
        
        resolve(recordings);
      };
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Update recording metadata
   * @param {string} id - Recording ID
   * @param {Object} updates - Fields to update
   * @returns {Promise<boolean>} Success status
   */
  async updateRecording(id, updates) {
    if (!this.db) {
      await this.initialize();
    }

    try {
      const recording = await this.getRecording(id);
      if (!recording) {
        throw new Error(`Recording not found: ${id}`);
      }

      // Merge updates
      const updatedRecord = {
        ...recording,
        ...updates,
        updatedAt: Date.now()
      };

      // Convert blob back to base64 if it exists
      if (updatedRecord.audioBlob) {
        updatedRecord.audioData = await this.blobToBase64(updatedRecord.audioBlob);
        delete updatedRecord.audioBlob;
      }

      return new Promise((resolve, reject) => {
        const transaction = this.db.transaction([this.storeName], 'readwrite');
        const store = transaction.objectStore(this.storeName);
        const request = store.put(updatedRecord);

        request.onsuccess = () => {
          console.log(`📝 Updated recording: ${id}`);
          resolve(true);
        };
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error('❌ Failed to update recording:', error);
      throw error;
    }
  }

  /**
   * Delete recording by ID
   * @param {string} id - Recording ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteRecording(id) {
    if (!this.db) {
      await this.initialize();
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const request = store.delete(id);

      request.onsuccess = () => {
        console.log(`🗑️ Deleted recording: ${id}`);
        resolve(true);
      };
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Get storage statistics
   * @returns {Promise<Object>} Storage stats
   */
  async getStorageStats() {
    const recordings = await this.getAllRecordings();
    
    const totalSize = recordings.reduce((sum, r) => sum + r.size, 0);
    const totalDuration = recordings.reduce((sum, r) => sum + r.duration, 0);
    const categories = [...new Set(recordings.map(r => r.category))];
    
    return {
      totalRecordings: recordings.length,
      totalSize,
      totalDuration,
      categories,
      averageSize: recordings.length > 0 ? totalSize / recordings.length : 0,
      averageDuration: recordings.length > 0 ? totalDuration / recordings.length : 0
    };
  }

  /**
   * Convert blob to base64 string
   */
  async blobToBase64(blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64 = reader.result.split(',')[1]; // Remove data URL prefix
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  /**
   * Convert base64 string to blob
   */
  base64ToBlob(base64String) {
    const binaryString = atob(base64String);
    const bytes = new Uint8Array(binaryString.length);
    
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    
    return new Blob([bytes], { type: 'audio/wav' });
  }

  /**
   * Close database connection
   */
  close() {
    if (this.db) {
      this.db.close();
      this.db = null;
      console.log('💾 IndexedDB connection closed');
    }
  }
}

export default IndexedDBController;
