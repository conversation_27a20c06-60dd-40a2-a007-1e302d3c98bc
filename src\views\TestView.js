import { baseStyles, applyStyles, addHoverEffect } from '../styles/base.js';

// SVG microphone icon as string
const microphoneIcon = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
  <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"/>
  <path d="M19 10v2a7 7 0 0 1-14 0v-2a1 1 0 0 1 2 0v2a5 5 0 0 0 10 0v-2a1 1 0 0 1 2 0z"/>
  <path d="M12 19a1 1 0 0 1 1 1v1a1 1 0 0 1-2 0v-1a1 1 0 0 1 1-1z"/>
</svg>`;

class TestView {
  constructor() {
    this.container = null;
  }

  render() {
    // Create main container
    this.container = document.createElement('div');
    applyStyles(this.container, baseStyles.container);

    // Create test text
    const testText = document.createElement('p');
    testText.textContent = 'I went to the store to buy some groceries. The store was busy, and there was a long line at the checkout. I still managed to get everything I needed before going home.';
    applyStyles(testText, baseStyles.testText);

    // Create floating microphone button
    const floatingMic = document.createElement('button');
    floatingMic.innerHTML = microphoneIcon;
    
    // Apply base styles and hover effects
    addHoverEffect(
      floatingMic,
      { ...baseStyles.floatingMicrophone },
      baseStyles.floatingMicrophoneHover
    );

    // Style the SVG icon
    const svgIcon = floatingMic.querySelector('svg');
    if (svgIcon) {
      applyStyles(svgIcon, baseStyles.microphoneIcon);
    }

    // Add click handler for recording functionality
    floatingMic.addEventListener('click', () => {
      // TODO: Implement recording functionality
      console.log('Recording started/stopped');
    });

    // Append elements
    this.container.appendChild(testText);
    document.body.appendChild(floatingMic); // Floating mic is positioned fixed

    // Store reference to floating mic for cleanup
    this.floatingMic = floatingMic;

    return this.container;
  }

  destroy() {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
    
    // Remove floating microphone
    if (this.floatingMic && this.floatingMic.parentNode) {
      this.floatingMic.parentNode.removeChild(this.floatingMic);
    }
  }
}

export default TestView;
