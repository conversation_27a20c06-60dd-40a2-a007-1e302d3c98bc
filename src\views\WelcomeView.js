import TransitionController from '../utils/TransitionController.js';

// SVG microphone icon as string
const microphoneIcon = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
  <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"/>
  <path d="M19 10v2a7 7 0 0 1-14 0v-2a1 1 0 0 1 2 0v2a5 5 0 0 0 10 0v-2a1 1 0 0 1 2 0z"/>
  <path d="M12 19a1 1 0 0 1 1 1v1a1 1 0 0 1-2 0v-1a1 1 0 0 1 1-1z"/>
</svg>`;

class WelcomeView {
  constructor() {
    this.container = null;
  }

  render() {
    // Create main container
    this.container = document.createElement('div');
    this.container.className = 'container';

    // Create welcome text
    const welcomeText = document.createElement('h1');
    welcomeText.textContent = 'Wanna test how good your speaking skill is?';
    welcomeText.className = 'welcome-text';

    // Create microphone button
    const micButton = document.createElement('button');
    micButton.innerHTML = microphoneIcon;
    micButton.className = 'microphone-button';

    // Add view transition name for smooth animation
    micButton.style.viewTransitionName = 'microphone-button';

    // Style the SVG icon
    const svgIcon = micButton.querySelector('svg');
    if (svgIcon) {
      applyStyles(svgIcon, baseStyles.microphoneIcon);
    }

    // Add click handler with transition controller
    micButton.addEventListener('click', () => {
      TransitionController.navigateWelcomeToTest(() => {
        window.location.hash = '#/test';
      });
    });

    // Create demo link
    const demoLink = document.createElement('a');
    demoLink.textContent = 'View Transition Demo';
    demoLink.href = '#/demo';
    demoLink.className = 'demo-link';

    // Append elements
    this.container.appendChild(welcomeText);
    this.container.appendChild(micButton);
    this.container.appendChild(demoLink);

    // Trigger entrance animation
    setTimeout(() => {
      welcomeText.classList.add('visible');
      micButton.classList.add('visible');
    }, 100);

    return this.container;
  }

  destroy() {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
  }
}

export default WelcomeView;
