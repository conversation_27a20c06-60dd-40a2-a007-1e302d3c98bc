class Router {
  constructor() {
    this.routes = {};
    this.currentRoute = null;
    this.isInitialLoad = true;

    // Listen for hash changes
    window.addEventListener('hashchange', () => this.handleRouteChange());
    window.addEventListener('load', () => this.handleRouteChange());
  }

  addRoute(path, handler) {
    this.routes[path] = handler;
  }

  handleRouteChange() {
    const hash = window.location.hash.slice(1) || '/';
    const route = this.routes[hash];

    if (route) {
      this.currentRoute = hash;

      // Use view transition if supported and not the initial load
      if (document.startViewTransition && !this.isInitialLoad) {
        document.startViewTransition(() => {
          route();
        });
      } else {
        route();
      }

      // Mark that initial load is complete
      this.isInitialLoad = false;
    } else {
      // Default to home route
      window.location.hash = '#/';
    }
  }

  navigate(path) {
    window.location.hash = `#${path}`;
  }
}

export default Router;
