// Base styles as JavaScript objects for style components approach
export const baseStyles = {
  // Reset and base styles
  reset: {
    margin: '0',
    padding: '0',
    boxSizing: 'border-box'
  },

  body: {
    fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
    backgroundColor: '#e2e8f0',
    color: '#2d3748',
    lineHeight: '1.6',
    minHeight: '100vh',
    overflow: 'hidden'
  },

  container: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: '100vh',
    padding: '2rem',
    position: 'relative'
  },

  // Welcome page styles
  welcomeText: {
    fontSize: '2.5rem',
    fontWeight: '600',
    textAlign: 'center',
    color: '#2d3748',
    marginBottom: '3rem',
    maxWidth: '800px',
    lineHeight: '1.3',
    opacity: '0',
    transform: 'translateY(20px)'
  },

  welcomeTextVisible: {
    opacity: '1',
    transform: 'translateY(0)',
    transition: 'all 0.5s ease-out'
  },

  microphoneButton: {
    width: '80px',
    height: '80px',
    borderRadius: '50%',
    backgroundColor: '#4299e1',
    border: 'none',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: 'white',
    fontSize: '2rem',
    transition: 'all 0.3s ease',
    boxShadow: '0 4px 12px rgba(66, 153, 225, 0.3)',
    opacity: '0',
    transform: 'translateY(20px)'
  },

  microphoneButtonVisible: {
    opacity: '1',
    transform: 'translateY(0)',
    transition: 'all 0.5s ease-out',
    transitionDelay: '0.2s'
  },

  microphoneButtonHover: {
    backgroundColor: '#3182ce',
    transform: 'scale(1.05)',
    boxShadow: '0 6px 16px rgba(66, 153, 225, 0.4)',
    opacity: '1'
  },

  // Test page styles
  testText: {
    fontSize: '1.8rem',
    fontWeight: '500',
    textAlign: 'center',
    color: '#2d3748',
    maxWidth: '1200px',
    lineHeight: '1.6',
    marginBottom: '2rem',
    opacity: '1'
  },

  testTextVisible: {
    opacity: '1'
  },

  floatingMicrophone: {
    position: 'fixed',
    bottom: '300px',
    left: '50%',
    transform: 'translateX(-50%)',
    width: '80px',
    height: '80px',
    borderRadius: '50%',
    backgroundColor: '#4299e1',
    border: 'none',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: 'white',
    fontSize: '2rem',
    boxShadow: '0 4px 12px rgba(66, 153, 225, 0.3)',
    opacity: '1'
  },

  floatingMicrophoneVisible: {
    opacity: '1',
    transform: 'translateX(-50%)'
  },

  floatingMicrophoneHover: {
    backgroundColor: '#3182ce',
    transform: 'translateX(-50%) scale(1.05)',
    boxShadow: '0 6px 16px rgba(66, 153, 225, 0.4)',
    opacity: '1',
    transition: 'all 0.3s ease'
  },

  microphoneIcon: {
    width: '24px',
    height: '24px',
    fill: 'currentColor'
  }
};

// Utility function to apply styles to elements
export function applyStyles(element, styles) {
  Object.assign(element.style, styles);
}

// Utility function to add hover effects
export function addHoverEffect(element, normalStyles, hoverStyles, visibleStyles = {}) {
  applyStyles(element, normalStyles);

  element.addEventListener('mouseenter', () => {
    applyStyles(element, { ...normalStyles, ...visibleStyles, ...hoverStyles });
  });

  element.addEventListener('mouseleave', () => {
    // Remove transition for instant return to normal state
    const { transition, ...stylesWithoutTransition } = { ...normalStyles, ...visibleStyles };
    applyStyles(element, stylesWithoutTransition);
  });
}
