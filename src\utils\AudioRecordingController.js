/**
 * AudioRecordingController - Manages Web Audio API recording with post-processing
 * Features: Noise suppression, echo cancellation, 16kHz resampling, WAV export
 */
class AudioRecordingController {
  constructor() {
    this.audioContext = null;
    this.mediaStream = null;
    this.mediaRecorder = null;
    this.sourceNode = null;
    this.processorNode = null;
    this.isRecording = false;
    this.recordedChunks = [];
    this.startTime = null;
    this.sampleRate = 16000; // Target sample rate
    this.recordedAudioData = [];
    
    // Recording constraints with noise suppression and echo cancellation
    this.constraints = {
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
        sampleRate: this.sampleRate,
        channelCount: 1 // Mono recording
      }
    };
  }

  /**
   * Initialize audio context and get microphone permission
   */
  async initialize() {
    try {
      // Create audio context with target sample rate
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)({
        sampleRate: this.sampleRate
      });

      // Get microphone access with constraints
      this.mediaStream = await navigator.mediaDevices.getUserMedia(this.constraints);
      
      console.log('🎤 Audio recording initialized successfully');
      console.log('📊 Audio context sample rate:', this.audioContext.sampleRate);
      
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize audio recording:', error);
      throw new Error(`Microphone access denied or not available: ${error.message}`);
    }
  }

  /**
   * Start recording audio
   */
  async startRecording() {
    if (this.isRecording) {
      console.warn('⚠️ Recording already in progress');
      return false;
    }

    try {
      // Always reinitialize for fresh recording session
      if (!this.audioContext || !this.mediaStream || this.audioContext.state === 'closed') {
        await this.initialize();
      }

      // Resume audio context if suspended
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume();
      }

      // Create source node from media stream
      this.sourceNode = this.audioContext.createMediaStreamSource(this.mediaStream);
      
      // Create script processor for real-time audio processing
      // Using 4096 buffer size for good balance between latency and processing
      this.processorNode = this.audioContext.createScriptProcessor(4096, 1, 1);
      
      // Reset recording data
      this.recordedAudioData = [];
      this.recordedChunks = [];
      this.startTime = Date.now();

      // Set up audio processing callback
      this.processorNode.onaudioprocess = (event) => {
        if (this.isRecording) {
          const inputBuffer = event.inputBuffer;
          const inputData = inputBuffer.getChannelData(0);
          
          // Copy audio data for WAV encoding
          const audioData = new Float32Array(inputData.length);
          audioData.set(inputData);
          this.recordedAudioData.push(audioData);
        }
      };

      // Connect audio nodes
      this.sourceNode.connect(this.processorNode);
      this.processorNode.connect(this.audioContext.destination);

      // Also set up MediaRecorder as backup
      this.setupMediaRecorder();

      this.isRecording = true;
      console.log('🔴 Recording started');
      
      return true;
    } catch (error) {
      console.error('❌ Failed to start recording:', error);
      throw error;
    }
  }

  /**
   * Set up MediaRecorder as backup recording method
   */
  setupMediaRecorder() {
    try {
      const options = {
        mimeType: 'audio/webm;codecs=opus',
        audioBitsPerSecond: 128000
      };

      this.mediaRecorder = new MediaRecorder(this.mediaStream, options);
      
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.recordedChunks.push(event.data);
        }
      };

      this.mediaRecorder.start(100); // Collect data every 100ms
    } catch (error) {
      console.warn('⚠️ MediaRecorder setup failed:', error);
    }
  }

  /**
   * Stop recording and return audio data
   */
  async stopRecording() {
    if (!this.isRecording) {
      console.warn('⚠️ No recording in progress');
      return null;
    }

    try {
      this.isRecording = false;
      const endTime = Date.now();
      const duration = (endTime - this.startTime) / 1000; // Duration in seconds

      // Stop MediaRecorder
      if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
        this.mediaRecorder.stop();
      }

      // Disconnect audio nodes
      if (this.sourceNode) {
        this.sourceNode.disconnect();
      }
      if (this.processorNode) {
        this.processorNode.disconnect();
      }

      console.log('⏹️ Recording stopped');
      console.log('⏱️ Duration:', duration.toFixed(2), 'seconds');
      console.log('📊 Audio chunks collected:', this.recordedAudioData.length);

      // Process and return audio data
      const audioData = this.processAudioData();

      // IMMEDIATELY release microphone resources after getting audio data
      this.releaseMicrophoneResources();

      return {
        audioData,
        duration,
        sampleRate: this.sampleRate,
        channels: 1,
        timestamp: this.startTime
      };
    } catch (error) {
      console.error('❌ Failed to stop recording:', error);
      // Ensure cleanup even on error
      this.releaseMicrophoneResources();
      throw error;
    }
  }

  /**
   * Immediately release microphone resources
   */
  releaseMicrophoneResources() {
    try {
      console.log('🔒 Releasing microphone resources...');

      // Stop all media stream tracks (this releases the microphone)
      if (this.mediaStream) {
        this.mediaStream.getTracks().forEach(track => {
          track.stop();
          console.log(`🔇 Stopped track: ${track.kind} (${track.label})`);
        });
        this.mediaStream = null;
      }

      // Disconnect and clear audio nodes
      if (this.sourceNode) {
        this.sourceNode.disconnect();
        this.sourceNode = null;
      }

      if (this.processorNode) {
        this.processorNode.disconnect();
        this.processorNode = null;
      }

      // Close audio context to free resources
      if (this.audioContext && this.audioContext.state !== 'closed') {
        this.audioContext.close();
        this.audioContext = null;
      }

      // Clear MediaRecorder
      this.mediaRecorder = null;

      console.log('✅ Microphone resources released successfully');
    } catch (error) {
      console.error('❌ Error releasing microphone resources:', error);
    }
  }

  /**
   * Process recorded audio data - combine chunks and ensure 16kHz
   */
  processAudioData() {
    if (this.recordedAudioData.length === 0) {
      console.warn('⚠️ No audio data recorded');
      return new Float32Array(0);
    }

    // Calculate total length
    const totalLength = this.recordedAudioData.reduce((sum, chunk) => sum + chunk.length, 0);
    
    // Combine all chunks into single array
    const combinedData = new Float32Array(totalLength);
    let offset = 0;
    
    for (const chunk of this.recordedAudioData) {
      combinedData.set(chunk, offset);
      offset += chunk.length;
    }

    // Resample to 16kHz if needed
    const currentSampleRate = this.audioContext.sampleRate;
    if (currentSampleRate !== this.sampleRate) {
      console.log(`🔄 Resampling from ${currentSampleRate}Hz to ${this.sampleRate}Hz`);
      return this.resampleAudio(combinedData, currentSampleRate, this.sampleRate);
    }

    return combinedData;
  }

  /**
   * Resample audio data to target sample rate
   */
  resampleAudio(audioData, fromSampleRate, toSampleRate) {
    if (fromSampleRate === toSampleRate) {
      return audioData;
    }

    const ratio = fromSampleRate / toSampleRate;
    const newLength = Math.round(audioData.length / ratio);
    const resampledData = new Float32Array(newLength);

    for (let i = 0; i < newLength; i++) {
      const sourceIndex = i * ratio;
      const index = Math.floor(sourceIndex);
      const fraction = sourceIndex - index;

      if (index + 1 < audioData.length) {
        // Linear interpolation
        resampledData[i] = audioData[index] * (1 - fraction) + audioData[index + 1] * fraction;
      } else {
        resampledData[i] = audioData[index] || 0;
      }
    }

    return resampledData;
  }

  /**
   * Get current recording state
   */
  getRecordingState() {
    return {
      isRecording: this.isRecording,
      duration: this.isRecording ? (Date.now() - this.startTime) / 1000 : 0,
      isInitialized: !!(this.audioContext && this.mediaStream)
    };
  }

  /**
   * Clean up resources (used when component is destroyed)
   */
  cleanup() {
    try {
      if (this.isRecording) {
        this.stopRecording();
      } else {
        // If not recording, still release any remaining resources
        this.releaseMicrophoneResources();
      }

      console.log('🧹 Audio recording cleanup completed');
    } catch (error) {
      console.error('❌ Error during cleanup:', error);
    }
  }
}

export default AudioRecordingController;
